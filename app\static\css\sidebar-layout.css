/* 左右布局专用样式 */

/* === 全局布局重置 === */
html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* === 主要布局容器 === */
.app-wrapper {
    display: flex;
    flex: 1;
    min-height: calc(100vh - 60px); /* 减去顶部工具栏高度 */
}

/* === 顶部工具栏 === */
.top-toolbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: var(--theme-primary, var(--bs-primary, #0d6efd));
    color: white;
    z-index: 1030;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: background-color 0.3s ease; /* 主题切换动画 */
}

.top-toolbar .toolbar-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.top-toolbar .toolbar-right {
    display: flex;
    align-items: center;
    gap: 10px;
}

.top-toolbar .brand-logo {
    height: 40px;
    max-width: 120px;
    object-fit: contain;
}

.top-toolbar .brand-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    text-decoration: none;
}

.top-toolbar .brand-text:hover {
    color: rgba(255,255,255,0.9);
}

/* 移动端菜单切换按钮 */
.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    display: none;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

/* === 左侧导航栏 === */
.sidebar {
    position: fixed;
    top: 60px;
    left: 0;
    width: 200px;
    height: calc(100vh - 60px);
    background: var(--theme-surface, var(--theme-gray-50, #f8f9fa));
    border-right: 1px solid var(--theme-gray-200, #dee2e6);
    overflow-y: auto;
    z-index: 1020;
    transition: transform 0.3s ease, background-color 0.3s ease, border-color 0.3s ease; /* 主题切换动画 */
}

.sidebar-nav {
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar-nav .nav-item {
    border-bottom: 1px solid #e9ecef;
}

.sidebar-nav .nav-link {
    display: block;
    padding: 12px 20px;
    color: var(--theme-gray-600, #495057);
    text-decoration: none;
    font-size: 14px; /* 统一字体大小 */
    font-weight: 400; /* 统一字体权重 */
    transition: all 0.2s ease;
    border: none;
    background: none;
}

.sidebar-nav .nav-link:hover {
    background: var(--theme-gray-100, #e9ecef);
    color: var(--theme-gray-800, #212529);
}

.sidebar-nav .nav-link.active {
    background: var(--theme-primary, var(--bs-primary, #0d6efd));
    color: white;
}

.sidebar-nav .nav-link i {
    width: 20px;
    text-align: center;
    margin-right: 10px;
}

/* 下拉菜单样式 */
.sidebar-nav .dropdown {
    position: relative;
}

.sidebar-nav .dropdown-toggle::after {
    float: right;
    margin-top: 8px;
}

.sidebar-nav .dropdown-menu {
    position: static;
    float: none;
    width: 100%;
    margin: 0;
    border: none;
    border-radius: 0;
    box-shadow: none;
    background: var(--theme-gray-100, #f1f3f4);
    display: none;
}

.sidebar-nav .dropdown.show .dropdown-menu {
    display: block;
}

.sidebar-nav .dropdown-item {
    padding: 10px 40px;
    font-size: 14px; /* 统一字体大小 */
    font-weight: 400; /* 统一字体权重 */
    color: var(--theme-gray-500, #6c757d);
    border-bottom: 1px solid var(--theme-gray-200, #e9ecef);
}

.sidebar-nav .dropdown-item:hover {
    background: var(--theme-gray-100, #e9ecef);
    color: var(--theme-gray-600, #495057);
}

.sidebar-nav .dropdown-header {
    padding: 8px 40px;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    text-transform: uppercase;
    background: #f8f9fa;
}

/* === 主内容区域 === */
.main-content {
    margin-left: 200px;
    margin-top: 60px;
    flex: 1;
    padding: 20px;
    min-height: calc(100vh - 60px - 80px); /* 减去顶部工具栏和底部高度 */
    background: var(--theme-surface, var(--bs-body-bg, #fff));
    color: var(--theme-text, var(--bs-body-color, #212529));
}

/* === 底部固定 === */
.footer {
    margin-left: 200px;
    margin-top: auto;
    background: var(--theme-surface, var(--theme-gray-50, #f8f9fa));
    border-top: 1px solid var(--theme-gray-200, #dee2e6);
    padding: 15px 20px;
    text-align: center;
    color: var(--theme-gray-500, #6c757d);
    font-size: 14px; /* 统一字体大小 */
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .sidebar-toggle {
        display: block;
    }
    
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .footer {
        margin-left: 0;
    }
    
    /* 移动端遮罩层 */
    .sidebar-overlay {
        position: fixed;
        top: 60px;
        left: 0;
        width: 100%;
        height: calc(100vh - 60px);
        background: rgba(0,0,0,0.5);
        z-index: 1019;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 15px;
    }

    .top-toolbar {
        padding: 0 15px;
    }

    .top-toolbar .brand-text {
        font-size: 16px; /* 统一字体大小 */
    }

    /* 移动端触摸优化 */
    .sidebar-nav .nav-link {
        min-height: 44px; /* 确保触摸目标大小 */
        display: flex;
        align-items: center;
        font-size: 16px; /* 移动端字体大小 */
        padding: 12px 20px;
    }

    .sidebar-nav .dropdown-item {
        min-height: 44px; /* 确保触摸目标大小 */
        display: flex;
        align-items: center;
        font-size: 16px; /* 移动端字体大小 */
        padding: 12px 40px;
    }

    .toolbar-btn {
        min-height: 44px; /* 确保触摸目标大小 */
        padding: 12px 16px;
        font-size: 16px; /* 移动端字体大小 */
    }
}

/* === 工具栏按钮样式 === */
.toolbar-btn {
    background: none;
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px; /* 统一字体大小 */
    font-weight: 400; /* 统一字体权重 */
    transition: background 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    min-height: 36px; /* 确保触摸目标大小 */
}

.toolbar-btn:hover {
    background: rgba(255,255,255,0.1);
    color: white;
    text-decoration: none;
}

.toolbar-btn .badge {
    position: absolute;
    top: 0;
    right: 0;
    transform: translate(25%, -25%);
}

/* === 下拉菜单优化 === */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 5px;
    border: none;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 1040;
    min-width: 200px;
}

/* === 通知样式 === */
.notification-dropdown {
    width: 320px;
    max-height: 400px;
    overflow-y: auto;
    padding: 0;
}

.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e9ecef;
    white-space: normal;
    display: block;
    color: inherit;
    text-decoration: none;
}

.notification-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: inherit;
}

.notification-item.unread {
    background-color: #f8f9fa;
}

.notification-title {
    font-weight: bold;
    margin-bottom: 0.25rem;
}

.notification-content {
    font-size: 0.875rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.75rem;
    color: #adb5bd;
    text-align: right;
}

/* === 主题适配 === */
/* 暗色主题适配 */
[data-theme="dark-neon"] .sidebar {
    background: var(--theme-surface-dark, #343a40);
    border-right-color: var(--theme-gray-600, #495057);
}

[data-theme="dark-neon"] .sidebar-nav .nav-link {
    color: var(--theme-text, #adb5bd);
}

[data-theme="dark-neon"] .sidebar-nav .nav-link:hover {
    background: var(--theme-gray-600, #495057);
    color: var(--theme-text, #fff);
}

[data-theme="dark-neon"] .main-content {
    background: var(--theme-surface, #212529);
    color: var(--theme-text, #fff);
}

[data-theme="dark-neon"] .footer {
    background: var(--theme-surface-dark, #343a40);
    border-top-color: var(--theme-gray-600, #495057);
    color: var(--theme-text, #adb5bd);
}

/* 特殊主题的顶部工具栏优化 */
[data-theme="dark-neon"] .top-toolbar {
    background: var(--theme-primary) !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
}

[data-theme="warning"] .top-toolbar,
[data-theme="minimal-dawn"] .top-toolbar {
    /* 橙色主题的阴影优化 */
    box-shadow: 0 2px 8px rgba(249, 115, 22, 0.2);
}

[data-theme="success"] .top-toolbar,
[data-theme="nature-eco"] .top-toolbar {
    /* 绿色主题的阴影优化 */
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

/* 全主题适配 - 确保所有主题都能正确显示 */
[data-theme] .sidebar {
    background: var(--theme-surface, var(--theme-gray-50, #f8f9fa));
    border-right-color: var(--theme-gray-200, #dee2e6);
}

[data-theme] .sidebar-nav .nav-link {
    color: var(--theme-gray-600, #495057);
}

[data-theme] .sidebar-nav .nav-link:hover {
    background: var(--theme-gray-100, #e9ecef);
    color: var(--theme-gray-800, #212529);
}

[data-theme] .sidebar-nav .nav-link.active {
    background: var(--theme-primary, #0d6efd);
    color: white;
}

[data-theme] .main-content {
    background: var(--theme-surface, var(--bs-body-bg, #fff));
    color: var(--theme-text, var(--bs-body-color, #212529));
}

[data-theme] .footer {
    background: var(--theme-surface, var(--theme-gray-50, #f8f9fa));
    border-top-color: var(--theme-gray-200, #dee2e6);
    color: var(--theme-gray-500, #6c757d);
}

/* === 主题切换器样式 === */
.theme-switcher-panel {
    max-height: 400px;
    overflow-y: auto;
    min-width: 280px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.1);
}

.theme-option {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 16px;
    transition: background-color 0.2s ease;
}

.theme-option:hover {
    background-color: rgba(0,0,0,0.05);
}

.theme-preview {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: inline-block;
}

.theme-preview.primary { background: #0d6efd; }
.theme-preview.secondary { background: #6c757d; }
.theme-preview.success { background: #198754; }
.theme-preview.warning { background: #ffc107; }
.theme-preview.info { background: #0dcaf0; }
.theme-preview.danger { background: #dc3545; }
.theme-preview.classic-neutral { background: #495057; }
.theme-preview.modern-neutral { background: #6f7681; }
.theme-preview.noble-elegant { background: #6f42c1; }
.theme-preview.royal-solemn { background: #20c997; }
.theme-preview.deep-sea-tech { background: #0066cc; }
.theme-preview.soft-morandi { background: #a8a8a8; }
.theme-preview.minimal-dawn { background: #fd7e14; }
.theme-preview.dark-neon { background: #212529; }
.theme-preview.nature-eco { background: #198754; }

.theme-favorite {
    margin-left: auto;
    color: #ffc107;
    cursor: pointer;
}

.theme-controls {
    background: rgba(0,0,0,0.02);
    border-top: 1px solid rgba(0,0,0,0.1);
}

/* === 容器内容适配 === */
.container-fluid {
    padding-left: 15px;
    padding-right: 15px;
}

/* === 确保内容不被遮挡 === */
.main-content .container,
.main-content .container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
}

/* === 系统样式兼容性增强 === */

/* 与 Bootstrap 5.3.6 兼容 */
.sidebar-nav .nav-link {
    --bs-nav-link-padding-x: 20px;
    --bs-nav-link-padding-y: 12px;
    --bs-nav-link-font-size: 14px;
    --bs-nav-link-font-weight: 400;
    --bs-nav-link-color: var(--theme-gray-600, #495057);
    --bs-nav-link-hover-color: var(--theme-gray-800, #212529);
}

/* 与主题系统完全兼容 - 确保顶部工具栏跟随系统主题 */
.top-toolbar {
    background: var(--theme-primary, var(--bs-primary, #0d6efd)) !important;
}

/* 确保所有主题的顶部工具栏都正确显示 */
[data-theme] .top-toolbar {
    background: var(--theme-primary) !important;
}

/* 确保所有主题的侧边栏都正确显示 */
[data-theme] .sidebar {
    background: var(--theme-surface, var(--theme-gray-50, #f8f9fa)) !important;
    border-right-color: var(--theme-gray-200, #dee2e6) !important;
}

/* 确保字体大小统一性 */
.sidebar-nav .nav-link,
.sidebar-nav .dropdown-item,
.toolbar-btn,
.footer {
    font-size: 14px !important; /* 桌面端统一字体大小 */
    font-weight: 400 !important; /* 统一字体权重 */
}

/* 移动端字体大小覆盖 */
@media (max-width: 768px) {
    .sidebar-nav .nav-link,
    .sidebar-nav .dropdown-item,
    .toolbar-btn {
        font-size: 16px !important; /* 移动端统一字体大小 */
    }
}

/* 与 mobile-optimization.css 兼容 */
@media (max-width: 768px) {
    .sidebar-nav .nav-link,
    .sidebar-nav .dropdown-item {
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
        touch-action: manipulation;
    }
}

/* 确保与其他CSS文件的优先级平衡 */
.sidebar {
    background: var(--theme-surface, var(--theme-gray-50, #f8f9fa)) !important;
}

.main-content {
    background: var(--theme-surface, var(--bs-body-bg, #fff)) !important;
    color: var(--theme-text, var(--bs-body-color, #212529)) !important;
}
